#include <rtdevice.h>
#include "lds_utils.h"
#include "lds_uart.h"

#define DBG_TAG "UAC"
#define DBG_LVL DBG_INFO
#include <rtdbg.h>

#define UAC_SERIAL_NAME         "uart3"
#define UAC_POWER_CTRL_PIN      "PE.15"

#define CMD_HEAD_MAGIC_1 0x55
#define CMD_HEAD_MAGIC_2 0xAA
#define UAC_HB_TIMEOUT   (RT_TICK_PER_SECOND * 20)   // 20S
#define UAC_CMD_LEN      6

static rt_base_t uac_power_ctrl = -1;  /* UAC电源控制引脚 */
static rt_device_t uac_dev = RT_NULL;
static struct rt_timer heartbeatTimer;

/**
 * @brief UAC command types enumeration
 * @details Defines the supported command types for UAC communication
 */
typedef enum
{
    CMD_KEY_HEARTBEAT=0x00,     /**< Heartbeat command */
    CMD_KEY_UP_DOWN=0x01,       /**< Key up/down command */
    CMD_KEY_RESP=0x02,          /**< Command response */
} UAC_CMD_E;

/**
 * @brief UAC key command structure
 * @details Structure for UAC command packets with fixed 6-byte format
 */
typedef struct
{
    uint8_t head1;              /**< First magic byte (0x55) */
    uint8_t head2;              /**< Second magic byte (0xAA) */
    uint8_t len;                /**< Command length */
    uint8_t cmd;                /**< Command type from UAC_CMD_E */
    uint8_t data;               /**< Command data payload */
    uint8_t crc;                /**< XOR checksum */
} uac_key_cmd_t;

typedef enum 
{
    UAC_STATE_IDLE = 0,        /**< Initial state */
    UAC_STATE_HEAD1,           /**< State for validating first magic byte */
    UAC_STATE_HEAD2,           /**< State for validating second magic byte */
    UAC_STATE_LEN,             /**< State for validating command length */
    UAC_STATE_COMMAND,         /**< State for validating command type */
    UAC_STATE_VALUE,           /**< State for validating command data */
} UAC_STATE_E;

static uint8_t cmdBuf[UAC_CMD_LEN];

static void ldsUacReset(void)
{
    if(uac_power_ctrl <= 0){
        LOG_E("UAC power control pin not initialized");
        return;
    }
    rt_pin_write(uac_power_ctrl, PIN_LOW);
    rt_thread_mdelay(200);
    rt_pin_write(uac_power_ctrl, PIN_HIGH);
}

/* 定时器 超时函数 */
static void ldsUacHeartbeatCheck(void* parameter)
{
    LOG_W("UAC heartbeat timeout\n");
    ldsUacReset();
}

void ldsUacKeyCmdsend(bool key_down)
{
    uac_key_cmd_t cmd = {0};
    uint8_t *data = (uint8_t *)&cmd;
    
    if(uac_dev == RT_NULL){
        LOG_E("UAC %s not initialized", UAC_SERIAL_NAME);
        return;
    }
    cmd.head1 = CMD_HEAD_MAGIC_1;
    cmd.head2 = CMD_HEAD_MAGIC_2;
    cmd.len = 0x03;
    cmd.cmd = CMD_KEY_UP_DOWN;
    cmd.data = key_down;
    cmd.crc = ldsUtilXorChecksum(data, UAC_CMD_LEN - 1);
    rt_device_write(uac_dev, 0, data, sizeof(cmd));
}

/**
 * @brief Process command format
 * @param data Pointer to command data
 * @param size Size of command data
 * @return int 0 on success, negative on error
 */
static int ldsUacProcessCommand(const uint8_t* data, rt_size_t size)
{
    
    uint8_t xor = 0;
    
    if (data == RT_NULL || size == 0)
    {
        LOG_E("Invalid data or size");
        return -RT_ERROR;
    }

    if(size != UAC_CMD_LEN){
        LOG_E("Invalid command length %d", size);
        LOG_HEX("uac", 8, data, size);
        return -1;
    }
    
    xor = ldsUtilXorChecksum(data, size - 1);
    if(xor != data[size - 1]){
        LOG_E("XOR checksum error, cal %02X, get %02X", xor, data[size - 1]);
        LOG_HEX("uac", 8, data, size);
        return -1;
    }
    
    if(data[0] != CMD_HEAD_MAGIC_1 || data[1] != CMD_HEAD_MAGIC_2){
        LOG_E("Invalid command head %02X %02X", data[0], data[1]);
        LOG_HEX("uac", 8, data, size);
        return -1;
    }

    switch (data[3])
    {
        case CMD_KEY_HEARTBEAT:
            {
                /* Reset heartbeat timer */
                rt_timer_stop(&heartbeatTimer);
                rt_timer_start(&heartbeatTimer);
                LOG_D("Heartbeat received");
            }
            break;
        case CMD_KEY_RESP:
            {
                LOG_I("UAC key command response %d", data[4]);
            }
            break;
        default:
            LOG_W("Unknown command type: 0x%02X", data[3]);
            break;
    }
    return 0;
}

/**
 * @brief UAC data processing function 
 *
 * @param dev RT-Thread device handle
 * @param data Pointer to received data buffer
 * @param size Size of received data in bytes
 * @return int 0 on success, negative error code on failure
 *
 * @warning Ensure proper initialization via ldsUacInit() before calling
 */
int ldsUacProcess(rt_device_t dev, const uint8_t* data, rt_size_t size)
{
    static UAC_STATE_E state = UAC_STATE_IDLE;
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    if (dev == RT_NULL) {
        LOG_E("Invalid device handle");
        return -RT_EINVAL;
    }

    LOG_D("Received %d bytes from %s", size, dev->parent.name);

    /* Process as streaming data */
    for (size_t i = 0; i < size; i++) {
        switch (state) {
            case UAC_STATE_IDLE:
                if (data[i] == CMD_HEAD_MAGIC_1) {
                    cmdBuf[0] = data[i];
                    state = UAC_STATE_HEAD1;
                } else {
                    LOG_E("Invalid head1: 0x%02X", data[i]);
                    state = UAC_STATE_IDLE;
                }
                break;
            case UAC_STATE_HEAD1:
                if (data[i] == CMD_HEAD_MAGIC_2) {
                    state = UAC_STATE_HEAD2;
                    cmdBuf[1] = data[i];
                } else {
                    LOG_E("Invalid head2: 0x%02X", data[i]);
                    state = UAC_STATE_IDLE;
                }
                break;
            case UAC_STATE_HEAD2:
                if (data[i] == 0x03) {
                    state = UAC_STATE_LEN;
                    cmdBuf[2] = data[i];
                } else {
                    LOG_E("Invalid len: 0x%02X", data[i]);
                    state = UAC_STATE_IDLE;
                }
                break;
            case UAC_STATE_LEN:
                cmdBuf[3] = data[i];
                state = UAC_STATE_COMMAND;
                break;
            case UAC_STATE_COMMAND:
                state = UAC_STATE_VALUE;
                cmdBuf[4] = data[i];
                break;
            case UAC_STATE_VALUE:
                cmdBuf[5] = data[i];
                if(ldsUacProcessCommand(cmdBuf, sizeof(cmdBuf))){
                    LOG_HEX("uac-o", 8, data, size);
                }
                state = UAC_STATE_IDLE;
                rt_memset(cmdBuf, 0, sizeof(cmdBuf));
                break;
        }
    }

    return 0;
}

/**
 * @brief Initialize UAC device with streaming support
 * @details Initializes UAC hardware, UART communication, streaming buffers,
 *          and background processing threads
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note This function performs complete UAC system initialization including:
 *       - Power control pin setup
 *       - UART interface initialization with DMA
 *       - Streaming buffer allocation and setup
 *       - Background processing thread creation
 *       - Heartbeat timer configuration
 */
int ldsUacInit(void)
{
    rt_memset(cmdBuf, 0, sizeof(cmdBuf));

    /* Initialize power control */
    uac_power_ctrl = power_ctrl_pin_init(UAC_POWER_CTRL_PIN, PIN_HIGH);
    if (uac_power_ctrl < 0) {
        LOG_E("Failed to initialize UAC power control pin %s", UAC_POWER_CTRL_PIN);
        return -RT_ERROR;
    }

    /* Reset UAC device */
    ldsUacReset();

    /* Initialize UART with callback */
    uac_dev = ldsUartInit(UAC_SERIAL_NAME, LDS_UART_INDEX_3, ldsUacProcess);
    if(uac_dev == RT_NULL) {
        LOG_E("Failed to initialize UAC UART %s", UAC_SERIAL_NAME);
        return -RT_ERROR;
    }

    /* Initialize heartbeat timer */
    rt_timer_init(&heartbeatTimer, "UAChb",
                  ldsUacHeartbeatCheck,
                  RT_NULL,
                  UAC_HB_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_PERIODIC);
    rt_timer_start(&heartbeatTimer);

    return 0;
}
